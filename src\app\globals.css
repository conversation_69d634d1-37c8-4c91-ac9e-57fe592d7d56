@import url('https://fonts.googleapis.com/css2?family=Bubblegum+Sans&family=Jost:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

@import "tailwindcss";

@theme {
  --color-background: #fff;
  --color-foreground: #686868;

  --color-primary: #F7941E;
  --color-primary-foreground: #F7941E;

  --color-secondary: #1CBBB4;
  --color-secondary-foreground: #1CBBB4;

  --color-destructive: #ED145B;
  --color-destructive-foreground: #ED145B;

  --color-green: #73BE48;
  --color-green-foreground: #73BE48;

  --color-warm: #FFF0E5;

  --color-cream-foreground: #fff;

  --color-muted: #000;
  --color-muted-foreground: #000;

  --color-border: hsl(214.3 31.8% 91.4%);
  --color-input: hsl(214.3 31.8% 91.4%);
  --color-ring: hsl(222.2 84% 4.9%);

  --radius: 0.5rem;

  --default-font-size: 16px;
  --default-line-height: 160%;
}

@layer base {
  .dark {
    --color-background: hsl(222.2 84% 4.9%);
    --color-foreground: hsl(210 40% 98%);

    --color-card: hsl(222.2 84% 4.9%);
    --color-card-foreground: hsl(210 40% 98%);

    --color-popover: hsl(222.2 84% 4.9%);
    --color-popover-foreground: hsl(210 40% 98%);

    --color-primary: hsl(210 40% 98%);
    --color-primary-foreground: hsl(222.2 47.4% 11.2%);

    --color-secondary: hsl(217.2 32.6% 17.5%);
    --color-secondary-foreground: hsl(210 40% 98%);

    --color-muted: hsl(217.2 32.6% 17.5%);
    --color-muted-foreground: hsl(215 20.2% 65.1%);

    --color-accent: hsl(217.2 32.6% 17.5%);
    --color-accent-foreground: hsl(210 40% 98%);

    --color-destructive: hsl(0 62.8% 30.6%);
    --color-destructive-foreground: hsl(210 40% 98%);

    --color-border: hsl(217.2 32.6% 17.5%);
    --color-input: hsl(217.2 32.6% 17.5%);
    --color-ring: hsl(212.7 26.8% 83.9%);
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-background text-foreground;
    font-size: var(--default-font-size);
    font-family: Nunito;
    color: var(--muted-foreground);
    line-height: var(--default-line-height);
    background-color: var(--background);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: Jost;
  }

  p {
    color: var(--foreground);

  }
}

@utility stroke-primary {
  -webkit-text-fill-color: var(--color-cream-foreground);
  -webkit-text-stroke-width: 2px;
  -webkit-text-stroke-color: #686868;
}

@layer components {

  /* ------- card hover image slide up start -------- */
  .image-layer-hover {
    background-size: cover;
    width: 25%;
    height: 100%;
    transition: 0.5s;
  }

  .image-layer-hover:nth-child(1) {
    background-position: 0;
    transition-delay: 0;
  }

  .image-layer-hover:nth-child(2) {
    background-position: 33.33%;
    transition-delay: 0.1s;
  }

  .image-layer-hover:nth-child(3) {
    background-position: 66.66%;
    transition-delay: 0.2s;
  }

  .image-layer-hover:nth-child(4) {
    background-position: 100%;
    transition-delay: 0.3s;
  }

  .layer-card:hover .image-layer-hover {
    transform: translateY(-100%);
  }

  /* ------- card hover image slide up end -------- */
}


/* service swiper pagination */
.services .swiper-pagination-bullets.swiper-pagination-horizontal {
  left: 0;
  top: 0;
  width: auto;
  display: flex;
  gap: 10px;
}

.services .swiper-pagination-bullet {
  height: 5px;
  width: 15px;
  border-radius: 10px;
  display: block;
  background-color: #F2F2F2;
}

.services .swiper-pagination-bullet-active {
  width: 33px;
  background-color: var(--secondary);
}

/* service swiper pagination */